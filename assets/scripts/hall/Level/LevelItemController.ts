// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import { LevelStatus } from "./LevelSelectController";

const { ccclass, property } = cc._decorator;

@ccclass
export default class LevelItemController extends cc.Component {

    @property(cc.Sprite)
    levelSprite: cc.Sprite = null;

    @property(cc.Label)
    levelLabel: cc.Label = null;

    @property(cc.Button)
    levelButton: cc.Button = null;

    private levelNumber: number = 1;
    private levelStatus: LevelStatus = LevelStatus.LOCKED;
    private isSelected: boolean = false;

    // 性能优化：缓存已加载的资源
    private static spriteFrameCache: Map<string, cc.SpriteFrame> = new Map();
    private currentImagePath: string = ""; // 记录当前使用的图片路径

    onLoad() {
        // 初始化组件引用
        if (!this.levelSprite) {
            this.levelSprite = this.getComponent(cc.Sprite);
        }
        if (!this.levelLabel) {
            this.levelLabel = this.getComponentInChildren(cc.Label);
        }
        if (!this.levelButton) {
            this.levelButton = this.getComponent(cc.Button);
        }
    }

    start() {
        // 确保在start时更新外观
        this.updateAppearance();
    }

    /**
     * 设置关卡数据
     */
    public setLevelData(levelNumber: number, status: LevelStatus, selected: boolean = false) {
        this.levelNumber = levelNumber;
        this.levelStatus = status;
        this.isSelected = selected;

        // 更新标签文本
        if (this.levelLabel) {
            this.levelLabel.string = levelNumber.toString();
        }

        this.updateAppearance();
    }

    /**
     * 检查是否为特殊关卡（第5、10、15、20、25关）
     */
    private isSpecialLevel(levelNumber: number): boolean {
        return levelNumber % 5 === 0;
    }

    /**
     * 更新外观
     */
    private updateAppearance() {
        if (!this.levelSprite) return;

        let imagePath = "";
        let size = cc.size(46, 46);

        // 检查是否为特殊关卡（第5、10、15、20、25关）
        const isSpecialLevel = this.isSpecialLevel(this.levelNumber);
        const uiSuffix = isSpecialLevel ? "01" : "";


        // 根据状态和是否选中确定图片路径和大小
        if (this.isSelected) {
            size = cc.size(86, 86);
            switch (this.levelStatus) {
                case LevelStatus.LOCKED:
                    imagePath = `hall_page_res/Level_Btn/pop_gray${uiSuffix}_choose`;
                    break;
                case LevelStatus.CURRENT:
                    imagePath = `hall_page_res/Level_Btn/pop_yellow${uiSuffix}_choose`;
                    break;
                case LevelStatus.COMPLETED:
                    imagePath = `hall_page_res/Level_Btn/pop_green${uiSuffix}_choose`;
                    break;
            }
        } else {
            switch (this.levelStatus) {
                case LevelStatus.LOCKED:
                    imagePath = `hall_page_res/Level_Btn/pop_gray${uiSuffix}`;
                    break;
                case LevelStatus.CURRENT:
                    imagePath = `hall_page_res/Level_Btn/pop_yellow${uiSuffix}`;
                    break;
                case LevelStatus.COMPLETED:
                    imagePath = `hall_page_res/Level_Btn/pop_green${uiSuffix}`;
                    break;
            }
        }

        // 设置节点大小
        this.node.setContentSize(size);

        // 优化的图片加载：只有当图片路径改变时才重新加载
        if (this.currentImagePath !== imagePath) {
            this.loadSpriteFrameOptimized(imagePath);
            this.currentImagePath = imagePath;
        }

        // 设置按钮交互状态
        if (this.levelButton) {
            this.levelButton.interactable = (this.levelStatus !== LevelStatus.LOCKED);
        }

        // 设置标签样式
        this.setupLabelStyle();
    }

    /**
     * 设置选中状态
     */
    public setSelected(selected: boolean) {
        if (this.isSelected !== selected) {
            this.isSelected = selected;
            this.updateAppearance();
        }
    }

    /**
     * 获取关卡号
     */
    public getLevelNumber(): number {
        return this.levelNumber;
    }

    /**
     * 获取关卡状态
     */
    public getLevelStatus(): LevelStatus {
        return this.levelStatus;
    }

    /**
     * 是否选中
     */
    public getIsSelected(): boolean {
        return this.isSelected;
    }

    /**
     * 设置标签样式
     */
    private setupLabelStyle() {
        if (!this.levelLabel) {
            cc.warn("LevelItemController: levelLabel is null");
            return;
        }

        // 设置字体大小为30
        this.levelLabel.fontSize = 30;

        // 设置颜色为白色 #FFFFFF
        this.levelLabel.node.color = cc.color(255, 255, 255);

        // 设置居中对齐
        this.levelLabel.horizontalAlign = cc.Label.HorizontalAlign.CENTER;
        this.levelLabel.verticalAlign = cc.Label.VerticalAlign.CENTER;

        // 注意：Cocos Creator的Label组件不支持直接设置加粗
        // 如需加粗效果，需要使用加粗字体文件

        // 添加外边框 LabelOutline
        let outline = this.levelLabel.getComponent(cc.LabelOutline);
        if (!outline) {
            outline = this.levelLabel.addComponent(cc.LabelOutline);
        }

        // 根据关卡状态设置边框颜色
        let outlineColor: cc.Color;
        switch (this.levelStatus) {
            case LevelStatus.LOCKED:
                // 未解锁边框为 #7B7B7B
                outlineColor = cc.color(123, 123, 123);
                break;
            case LevelStatus.CURRENT:
                // 当前玩到的关卡边框 #CF5800
                outlineColor = cc.color(207, 88, 0);
                break;
            case LevelStatus.COMPLETED:
                // 已解锁边框为 #119C0F
                outlineColor = cc.color(17, 156, 15);
                break;
            default:
                outlineColor = cc.color(123, 123, 123);
                break;
        }

        outline.color = outlineColor;
        outline.width = 1;

        // 确保标签位置居中
        this.levelLabel.node.setPosition(0, 0);
    }

    /**
     * 优化的图片加载方法：使用缓存避免重复加载
     * @param imagePath 图片路径
     */
    private loadSpriteFrameOptimized(imagePath: string) {
        // 先检查缓存
        const cachedSpriteFrame = LevelItemController.spriteFrameCache.get(imagePath);
        if (cachedSpriteFrame && this.levelSprite) {
            this.levelSprite.spriteFrame = cachedSpriteFrame;
            return;
        }

        // 缓存中没有，异步加载
        cc.resources.load(imagePath, cc.SpriteFrame, (err, spriteFrame) => {
            if (!err && spriteFrame && this.levelSprite) {
                // 设置图片
                this.levelSprite.spriteFrame = spriteFrame as cc.SpriteFrame;
                // 缓存图片
                LevelItemController.spriteFrameCache.set(imagePath, spriteFrame as cc.SpriteFrame);
            } else if (err) {
                cc.error(`Failed to load sprite: ${imagePath}`, err);
            }
        });
    }

    /**
     * 关卡点击事件
     */
    public onLevelClick() {
        if (this.levelStatus === LevelStatus.LOCKED) {
            
            return;
        }

        // 发送关卡选择事件
        this.node.emit('level-selected', this.levelNumber);
        
    }
}
