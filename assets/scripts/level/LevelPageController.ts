// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import { ExtendLevelInfoResponse } from "../bean/GameBean";
import LeaveDialogController from "../hall/LeaveDialogController";
import { Tools } from "../util/Tools";
import { Config } from "../util/Config";
import GlobalManagerController, { PageType } from "../GlobalManagerController";

const { ccclass, property } = cc._decorator;

@ccclass
export default class LevelPageController extends cc.Component {

    // 返回按钮
    @property(cc.Node)
    backButton: cc.Node = null;

    // 开始游戏按钮
    @property(cc.Button)
    startGameButton: cc.Button = null;

    // 地雷数UI标签
    @property(cc.Label)
    mineCountLabel: cc.Label = null;

    // 当前关卡数UI标签
    @property(cc.Label)
    currentLevelLabel: cc.Label = null;

    // 退出游戏弹窗
    @property(LeaveDialogController)
    leaveDialogController: LeaveDialogController = null;

    // level_page节点
    @property(cc.Node)
    levelPageNode: cc.Node = null;

    // game_map_1节点
    @property(cc.Node)
    gameMap1Node: cc.Node = null;

    // game_map_2节点
    @property(cc.Node)
    gameMap2Node: cc.Node = null;

    // 方形地图节点引用
    @property(cc.Node)
    qipan8x8Node: cc.Node = null; // level_page/game_map_1/chess_bg/qipan8*8

    @property(cc.Node)
    qipan8x9Node: cc.Node = null; // level_page/game_map_1/chess_bg/qipan8*9

    @property(cc.Node)
    qipan9x9Node: cc.Node = null; // level_page/game_map_1/chess_bg/qipan9*9

    @property(cc.Node)
    qipan9x10Node: cc.Node = null; // level_page/game_map_1/chess_bg/qipan9*10

    @property(cc.Node)
    qipan10x10Node: cc.Node = null; // level_page/game_map_1/chess_bg/qipan10*10

    // 特殊关卡节点引用
    @property(cc.Node)
    levelS001Node: cc.Node = null; // level_page/game_map_2/game_bg/Level_S001

    @property(cc.Node)
    levelS002Node: cc.Node = null; // level_page/game_map_2/game_bg/Level_S002

    @property(cc.Node)
    levelS003Node: cc.Node = null; // level_page/game_map_2/game_bg/Level_S003

    @property(cc.Node)
    levelS004Node: cc.Node = null; // level_page/game_map_2/game_bg/Level_S004

    @property(cc.Node)
    levelS005Node: cc.Node = null; // level_page/game_map_2/game_bg/Level_S005

    @property(cc.Node)
    levelS006Node: cc.Node = null; // level_page/game_map_2/game_bg/Level_S006

    // 当前关卡数据
    private currentLevel: number = 1;
    private currentLevelInfo: ExtendLevelInfoResponse = null;
    private currentRoomId: number = 0; // 当前关卡游戏的房间ID

    // 性能优化相关
    private lastShownMapNode: cc.Node = null; // 记录上次显示的地图节点
    private isUpdating: boolean = false; // 防止重复更新

    onLoad() {
        // 设置返回按钮点击事件 - 使用与GamePageController相同的样式
        if (this.backButton) {
            Tools.imageButtonClick(this.backButton, Config.buttonRes + 'side_btn_back_normal', Config.buttonRes + 'side_btn_back_pressed', () => {
                this.onBackButtonClick();
            });
        }

        // 设置开始游戏按钮点击事件
        if (this.startGameButton) {
            this.startGameButton.node.on('click', this.onStartGameButtonClick, this);
        }

        // 注意：不在onLoad中初始化关卡数UI，等待外部调用setCurrentLevel时再更新
    }

    start() {
        // 初始化时隐藏所有地图节点
        this.hideAllMapNodes();

    }

    /**
     * 返回按钮点击事件
     */
    private onBackButtonClick() {
        // 如果没有有效的房间ID，说明还没有进入游戏，直接返回关卡选择页面
        if (this.currentRoomId <= 0) {
            cc.log("没有有效的房间ID，直接返回关卡选择页面");
            this.returnToLevelSelect();
            return;
        }

        // 弹出确认退出对话框，type=1表示退出本局游戏，传递当前房间ID
        if (this.leaveDialogController) {

            this.leaveDialogController.show(1, () => {

            }, this.currentRoomId);
        } else {
            cc.warn("LeaveDialogController 未配置");
        }
    }

    /**
     * 返回到关卡选择页面
     */
    private returnToLevelSelect() {
        // 查找GlobalManagerController并切换到大厅页面
        const globalManagerNode = cc.find("Canvas/global_node") || cc.find("global_node");
        if (globalManagerNode) {
            const globalManager = globalManagerNode.getComponent("GlobalManagerController");
            if (globalManager) {
                globalManager.setCurrentPage(2); // PageType.HALL_PAGE = 2
            }
        }
    }

    /**
     * 开始游戏按钮点击事件
     */
    private onStartGameButtonClick() {
        // ExtendLevelInfo消息现在由LevelSelectPageController发送
        // 这里直接进入游戏，等待后端响应
        cc.log("开始游戏按钮被点击，等待关卡信息...");
    }

    /**
     * 处理ExtendLevelInfo响应
     * @param levelInfo 关卡信息响应数据
     */
    public onExtendLevelInfo(levelInfo: ExtendLevelInfoResponse) {
    

        this.currentLevelInfo = levelInfo;

        // 保存房间ID，用于退出时使用
        if (levelInfo.roomId) {
            this.currentRoomId = levelInfo.roomId;
           
        }

        // 更新地雷数UI
        this.updateMineCountUI(levelInfo.mineCount);

        // 使用当前设置的关卡编号，而不是后端返回的levelId
        // 因为后端的levelId可能与前端的关卡编号不一致
       
        this.enterLevel(this.currentLevel);
    }

    /**
     * 更新地雷数UI
     * @param mineCount 地雷数量
     */
    private updateMineCountUI(mineCount: number) {
        if (this.mineCountLabel) {
            this.mineCountLabel.string = mineCount.toString();
           
        }
    }

    /**
     * 更新当前关卡数UI
     * @param levelNumber 关卡编号
     */
    private updateCurrentLevelUI(levelNumber: number) {
        if (this.currentLevelLabel) {
            this.currentLevelLabel.string = `第${levelNumber}关`;
           
        }
    }

    /**
     * 根据关卡数进入相应的关卡（优化版本）
     * @param levelNumber 关卡编号
     */
    private enterLevel(levelNumber: number) {
        // 防止重复更新
        if (this.isUpdating) {
            return;
        }
        this.isUpdating = true;

        // 更新关卡数UI显示
        this.updateCurrentLevelUI(levelNumber);

        // 获取目标地图节点和容器
        const targetMapInfo = this.getMapNodeByLevel(levelNumber);
        if (!targetMapInfo) {
            cc.warn(`未知的关卡编号: ${levelNumber}`);
            this.isUpdating = false;
            return;
        }

        // 只有当目标节点与当前显示的节点不同时才进行切换
        if (this.lastShownMapNode !== targetMapInfo.mapNode) {
            // 隐藏上一个显示的地图节点
            if (this.lastShownMapNode) {
                this.lastShownMapNode.active = false;
            }

            // 显示目标容器和地图节点
            this.showMapContainer(targetMapInfo.containerType);
            this.showMapNodeOptimized(targetMapInfo.mapNode, targetMapInfo.mapName);

            // 记录当前显示的节点
            this.lastShownMapNode = targetMapInfo.mapNode;
        }

        this.isUpdating = false;
    }

    /**
     * 根据关卡数获取对应的地图节点信息
     * @param levelNumber 关卡编号
     */
    private getMapNodeByLevel(levelNumber: number): {mapNode: cc.Node, mapName: string, containerType: 'map1' | 'map2'} | null {
        if (levelNumber >= 1 && levelNumber <= 4) {
            return {mapNode: this.qipan8x8Node, mapName: "qipan8*8", containerType: 'map1'};
        } else if (levelNumber === 5) {
            return {mapNode: this.levelS001Node, mapName: "Level_S001", containerType: 'map2'};
        } else if (levelNumber >= 6 && levelNumber <= 9) {
            return {mapNode: this.qipan8x9Node, mapName: "qipan8*9", containerType: 'map1'};
        } else if (levelNumber === 10) {
            return {mapNode: this.levelS002Node, mapName: "Level_S002", containerType: 'map2'};
        } else if (levelNumber >= 11 && levelNumber <= 14) {
            return {mapNode: this.qipan9x9Node, mapName: "qipan9*9", containerType: 'map1'};
        } else if (levelNumber === 15) {
            return {mapNode: this.levelS003Node, mapName: "Level_S003", containerType: 'map2'};
        } else if (levelNumber >= 16 && levelNumber <= 19) {
            return {mapNode: this.qipan9x10Node, mapName: "qipan9*10", containerType: 'map1'};
        } else if (levelNumber === 20) {
            return {mapNode: this.levelS004Node, mapName: "Level_S004", containerType: 'map2'};
        } else if (levelNumber >= 21 && levelNumber <= 24) {
            return {mapNode: this.qipan10x10Node, mapName: "qipan10*10", containerType: 'map1'};
        } else if (levelNumber === 25) {
            return {mapNode: this.levelS005Node, mapName: "Level_S005", containerType: 'map2'};
        } else if (levelNumber >= 26 && levelNumber <= 29) {
            return {mapNode: this.qipan10x10Node, mapName: "qipan10*10", containerType: 'map1'};
        } else if (levelNumber === 30) {
            return {mapNode: this.levelS006Node, mapName: "Level_S006", containerType: 'map2'};
        }
        return null;
    }

    /**
     * 显示指定的地图节点（优化版本）
     * @param mapNode 要显示的地图节点
     * @param mapName 地图名称（用于日志）
     */
    private showMapNodeOptimized(mapNode: cc.Node, mapName: string) {
        if (mapNode) {
            mapNode.active = true;
        } else {
            cc.warn(`❌ 地图节点未找到: ${mapName}`);
        }
    }

    /**
     * 显示指定的地图容器
     * @param containerType 容器类型
     */
    private showMapContainer(containerType: 'map1' | 'map2') {
        // 确保 level_page 节点是激活的
        if (this.levelPageNode) {
            this.levelPageNode.active = true;
        }

        // 根据容器类型显示对应容器，隐藏另一个
        if (containerType === 'map1') {
            if (this.gameMap1Node && !this.gameMap1Node.active) {
                this.gameMap1Node.active = true;
            }
            if (this.gameMap2Node && this.gameMap2Node.active) {
                this.gameMap2Node.active = false;
            }
        } else {
            if (this.gameMap2Node && !this.gameMap2Node.active) {
                this.gameMap2Node.active = true;
            }
            if (this.gameMap1Node && this.gameMap1Node.active) {
                this.gameMap1Node.active = false;
            }
        }
    }

    /**
     * 隐藏所有地图节点
     */
    private hideAllMapNodes() {
        const allMapNodes = [
            this.qipan8x8Node,
            this.qipan8x9Node,
            this.qipan9x9Node,
            this.qipan9x10Node,
            this.qipan10x10Node,
            this.levelS001Node,
            this.levelS002Node,
            this.levelS003Node,
            this.levelS004Node,
            this.levelS005Node,
            this.levelS006Node
        ];

        allMapNodes.forEach(node => {
            if (node) {
                node.active = false;
            }
        });
    }

    /**
     * 设置当前关卡（从外部调用）
     * @param levelNumber 关卡编号
     */
    public setCurrentLevel(levelNumber: number) {
      
        this.currentLevel = levelNumber;


        // 立即根据关卡数切换地图显示
        this.enterLevel(levelNumber);
    }

    /**
     * 获取当前关卡编号
     */
    public getCurrentLevel(): number {
        return this.currentLevel;
    }

    /**
     * 获取当前关卡信息
     */
    public getCurrentLevelInfo(): ExtendLevelInfoResponse {
        return this.currentLevelInfo;
    }

    /**
     * 获取当前房间ID
     */
    public getCurrentRoomId(): number {
        return this.currentRoomId;
    }

    /**
     * 隐藏所有地图容器
     */
    private hideAllMapContainers() {
        // 确保 level_page 节点是激活的
        if (this.levelPageNode) {
            this.levelPageNode.active = true;
          
        }

        // 隐藏两个主要的地图容器
        if (this.gameMap1Node) {
            this.gameMap1Node.active = false;
            
        }
        if (this.gameMap2Node) {
            this.gameMap2Node.active = false;
           
        }

        // 同时隐藏所有具体的地图节点
        this.hideAllMapNodes();
    }

    /**
     * 显示 game_map_1 容器（方形地图）
     */
    private showGameMap1() {
        if (this.gameMap1Node) {
            this.gameMap1Node.active = true;
            
        } else {
            cc.warn("❌ game_map_1 节点未找到");
        }
    }

    /**
     * 显示 game_map_2 容器（特殊关卡）
     */
    private showGameMap2() {
        if (this.gameMap2Node) {
            this.gameMap2Node.active = true;
           
        } else {
            cc.warn("❌ game_map_2 节点未找到");
        }
    }
}
