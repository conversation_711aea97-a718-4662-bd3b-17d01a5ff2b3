# 关卡选择页面性能优化说明

## 优化前的主要性能问题

1. **频繁的UI更新**: 每次关卡切换都会遍历所有关卡节点更新外观
2. **重复的资源加载**: 每次更新外观都会重新加载图片资源，没有缓存机制
3. **不必要的节点操作**: 每次切换都会隐藏所有地图节点再显示目标节点
4. **滚动事件频繁触发**: 滚动过程中会频繁触发位置更新
5. **同步的资源加载**: 阻塞主线程导致卡顿

## 已实施的优化方案

### 1. LevelPageController.ts 优化

#### 1.1 添加状态管理
- 添加 `lastShownMapNode` 记录上次显示的地图节点
- 添加 `isUpdating` 防止重复更新

#### 1.2 优化地图切换逻辑
- **优化前**: 每次都隐藏所有地图容器，然后显示目标容器和节点
- **优化后**: 只有当目标节点与当前显示的节点不同时才进行切换
- 减少了不必要的DOM操作

#### 1.3 重构地图节点获取逻辑
- 将原来的长串if-else逻辑提取为 `getMapNodeByLevel()` 方法
- 提高代码可读性和维护性

#### 1.4 优化容器显示逻辑
- 新增 `showMapContainer()` 方法，只切换需要改变状态的容器
- 避免重复设置已经正确的状态

### 2. LevelSelectController.ts 优化

#### 2.1 添加防抖机制
- 添加 `updateTimer` 防抖定时器
- 添加 `isUpdatingDisplay` 防止重复更新显示

#### 2.2 优化关卡点击处理
- **优化前**: 每次点击都立即执行所有更新操作
- **优化后**: 
  - 如果点击的是当前已选中的关卡，直接返回
  - 使用50ms防抖延迟，避免频繁点击造成的性能问题
  - 清除之前的防抖定时器

#### 2.3 优化显示更新
- 使用 `cc.director.getScheduler().schedule()` 优化渲染时机
- 防止重复更新显示

### 3. LevelItemController.ts 优化

#### 3.1 添加资源缓存机制
- 添加静态 `spriteFrameCache` Map缓存已加载的图片资源
- 添加 `currentImagePath` 记录当前使用的图片路径

#### 3.2 优化图片加载
- **优化前**: 每次 `updateAppearance()` 都重新加载图片
- **优化后**: 
  - 只有当图片路径改变时才重新加载
  - 先检查缓存，如果有直接使用
  - 新加载的图片会自动缓存供下次使用

## 性能提升效果

### 1. 减少资源加载次数
- 图片资源只在第一次使用时加载，后续直接从缓存获取
- 大幅减少网络请求和文件IO操作

### 2. 减少DOM操作
- 地图节点切换时只操作必要的节点
- 避免重复设置相同的状态

### 3. 减少计算量
- 防抖机制避免频繁的重复计算
- 状态检查避免不必要的更新操作

### 4. 优化渲染时机
- 使用调度器优化UI更新时机
- 避免在同一帧内多次更新

## 建议的进一步优化

### 1. 对象池优化
```typescript
// 可以考虑为关卡节点实现对象池
class LevelNodePool {
    private static pool: cc.Node[] = [];
    
    static get(): cc.Node {
        return this.pool.pop() || this.createNew();
    }
    
    static put(node: cc.Node) {
        this.pool.push(node);
    }
}
```

### 2. 虚拟滚动
```typescript
// 对于大量关卡，可以实现虚拟滚动
// 只渲染可见区域的关卡节点
private renderVisibleLevels() {
    const visibleStart = Math.floor(this.scrollOffset / this.levelItemWidth);
    const visibleEnd = Math.min(visibleStart + this.visibleCount, this.totalLevels);
    // 只更新可见范围内的节点
}
```

### 3. 预加载优化
```typescript
// 可以在空闲时预加载下一批可能用到的资源
private preloadNextBatch() {
    const nextLevels = this.getNextLevels(5); // 预加载接下来5个关卡的资源
    nextLevels.forEach(level => {
        this.preloadLevelResources(level);
    });
}
```

## 使用建议

1. **测试性能**: 在不同设备上测试优化效果，特别是低端设备
2. **监控内存**: 注意缓存的内存使用，必要时实现LRU清理机制
3. **渐进优化**: 可以根据实际使用情况进一步调整防抖延迟等参数

这些优化应该能显著改善关卡选择页面的流畅度，特别是在频繁切换关卡时的性能表现。
